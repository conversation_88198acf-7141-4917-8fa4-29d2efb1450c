# 🔄 模型切换功能说明

## 功能概述

本功能允许用户在API额度用完或遇到错误时，无缝切换到其他可用的模型，并继续执行剩余的任务，而不需要重新开始整个对话。

## 主要特性

### 1. 智能错误检测
- 自动检测API额度超限错误 (`quota_exceeded`)
- 识别认证错误 (`authentication_error`)
- 显示用户友好的错误提示

### 2. 一键模型切换
- 点击"Switch Model"按钮直接打开设置抽屉
- 支持多种模型类型：
  - Gemini 免费API模型
  - Claude 模型
  - Ollama 本地模型

### 3. 任务连续性
- 保持对话历史记录
- 使用 `resume=true` 继续执行任务
- 无需重新输入指令

## 实现细节

### 前端改进

1. **错误处理组件** (`error-alert.tsx`)
   - 显示API错误信息
   - 提供重试和切换模型按钮
   - 根据错误类型显示相应操作

2. **模型切换逻辑** (`home.tsx`)
   ```typescript
   const handleSwitchModel = () => {
     setCurrentError(null);
     setIsSettingsOpen(true); // 打开设置抽屉
     toast.info("Please select a different model from the settings");
   };

   const handleModelChange = (newModel: string) => {
     // 如果有错误且切换了模型，自动重新初始化并继续任务
     if (currentError && oldModel !== newModel && socket) {
       // 重新初始化agent
       socket.send(JSON.stringify({
         type: "init_agent",
         content: { model_name: newModel, tool_args: toolSettings }
       }));
       
       // 继续执行任务
       socket.send(JSON.stringify({
         type: "query",
         content: { text: lastUserMessage.content, resume: true }
       }));
     }
   };
   ```

3. **设置抽屉集成**
   - 在主页面添加全局设置抽屉
   - 支持从错误状态直接打开设置

### 后端改进

1. **错误分类** (`ws_server.py`)
   ```python
   def classify_api_error(error_message: str, error_type: type = None) -> dict:
     # 检测额度超限错误
     if any(keyword in error_message_lower for keyword in quota_keywords):
       return {
         "error_type": "quota_exceeded",
         "user_friendly_message": "API quota exceeded. Please switch to a different model.",
         "suggestions": ["Switch to a different model if available"]
       }
   ```

2. **Agent重新初始化**
   ```python
   if msg_type == "init_agent":
     existing_agent = active_agents.get(websocket)
     existing_history = None
     
     if existing_agent:
       # 保存对话历史
       existing_history = existing_agent.history
       existing_agent.cancel()
     
     # 创建新agent
     agent = create_agent_for_connection(client, session_uuid, workspace_manager, websocket, tool_args)
     
     # 恢复对话历史
     if existing_history:
       agent.history = existing_history
   ```

## 使用流程

### 正常使用流程
1. 用户发送查询
2. Agent开始处理任务
3. 如果API正常，任务继续执行直到完成

### 错误处理流程
1. API返回额度超限错误
2. 前端显示错误提示和"Switch Model"按钮
3. 用户点击"Switch Model"打开设置抽屉
4. 用户选择新模型
5. 系统自动：
   - 重新初始化Agent使用新模型
   - 保持对话历史
   - 继续执行剩余任务

## 测试方法

### 1. 使用测试页面
打开 `test_model_switch.html` 在浏览器中：
```bash
# 启动后端服务器
python ws_server.py

# 在浏览器中打开测试页面
open test_model_switch.html
```

### 2. 使用Python测试脚本
```bash
python test_model_switching.py
```

### 3. 手动测试步骤
1. 启动前端和后端服务
2. 发送一个复杂任务（如创建Python脚本）
3. 在任务执行过程中切换模型
4. 验证任务是否继续执行

## 支持的模型

### Gemini 免费API
- `gemini-2.5-flash`
- `gemini-2.5-flash-preview-05-20`
- `gemini-2.5-pro-preview-05-06`

### Claude 模型
- `claude-3-7-sonnet@20250219`
- `claude-sonnet-4@20250514`
- `claude-opus-4@20250514`

### Ollama 本地模型
- `ollama:deepseek-r1:8b`
- `ollama:gemma3:latest`
- `ollama:llama3.3`
- `ollama:llama3.2`
- `ollama:mistral`
- `ollama:codellama`
- `ollama:phi3`
- `ollama:qwen2.5`

## 配置要求

### 环境变量
```bash
# Gemini API
GEMINI_API_KEY=your_gemini_api_key

# Ollama (可选，默认localhost:11434)
OLLAMA_HOST=http://localhost:11434

# Claude API (如果使用)
ANTHROPIC_API_KEY=your_anthropic_api_key
```

### 依赖安装
```bash
# 后端依赖
pip install -r requirements.txt

# 前端依赖
cd frontend
npm install
```

## 故障排除

### 常见问题

1. **模型切换后任务没有继续**
   - 检查WebSocket连接是否正常
   - 确认新模型已正确初始化
   - 查看控制台日志

2. **Ollama模型无法使用**
   - 确认Ollama服务正在运行
   - 检查OLLAMA_HOST环境变量
   - 验证模型是否已下载

3. **API密钥错误**
   - 检查环境变量设置
   - 确认API密钥有效且有足够额度

### 调试技巧

1. 打开浏览器开发者工具查看WebSocket消息
2. 检查后端日志文件
3. 使用测试页面逐步验证功能

## 未来改进

1. **自动模型选择**
   - 根据任务类型自动推荐最适合的模型
   - 智能负载均衡

2. **模型性能监控**
   - 跟踪各模型的响应时间和成功率
   - 提供模型使用统计

3. **批量任务处理**
   - 支持任务队列
   - 自动重试失败的任务

4. **更智能的错误恢复**
   - 自动检测最佳替代模型
   - 无缝切换，用户无感知
