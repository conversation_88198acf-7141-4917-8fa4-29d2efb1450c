services:
  frontend:
    build:
      context: ..
      dockerfile: docker/frontend/Dockerfile
    image: frontend
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    volumes:
      - ${WORKSPACE_PATH:-../workspace}:/app/workspace

  backend:
    build:
      context: ..
      dockerfile: docker/backend/Dockerfile
    image: backend
    init: true
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    env_file: 
      - ../.env
    environment:
      - COMPOSE_PROJECT_NAME=${COMPOSE_PROJECT_NAME}
      #Path of mounted file in docker
      - GOOGLE_APPLICATION_CREDENTIALS=/app/google-application-credentials.json
      - PROJECT_ID=${PROJECT_ID}
      - REGION=${REGION}
      - WORKSPACE_PATH=${WORKSPACE_PATH}
    volumes:
      - ${WORKSPACE_PATH:-../workspace}:/app/workspace
      #If file doesn't exist, use a dummy file
      - ${GOOGLE_APPLICATION_CREDENTIALS:-.dummy-credentials.json}:/app/google-application-credentials.json
