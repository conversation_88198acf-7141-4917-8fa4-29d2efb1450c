import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  output: "standalone",
  // 確保正確處理 UTF-8 編碼
  serverExternalPackages: [],
  // 設置響應頭以支持 UTF-8
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/json; charset=utf-8',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
