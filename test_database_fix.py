#!/usr/bin/env python3
"""
Test script to verify the database constraint fix.
"""

import asyncio
import json
import websockets
import uuid

async def test_database_fix():
    """Test that multiple connections don't cause database constraint errors."""
    device_id = str(uuid.uuid4())
    uri = f"ws://localhost:8000/ws?device_id={device_id}"
    
    print("🧪 Testing Database Constraint Fix")
    print("=" * 50)
    
    try:
        # First connection
        print("1️⃣ Establishing first connection...")
        async with websockets.connect(uri) as websocket1:
            print("✅ First connection established")
            
            # Initialize agent
            await websocket1.send(json.dumps({
                "type": "init_agent",
                "content": {
                    "model_name": "gemini-2.5-flash",
                    "tool_args": {
                        "deep_research": False,
                        "pdf": True,
                        "media_generation": True,
                        "audio_generation": True,
                        "browser": True,
                    }
                }
            }))
            
            # Wait for initialization
            response = await websocket1.recv()
            data = json.loads(response)
            print(f"📥 Received: {data.get('type')}")
            
            # Send a simple query
            await websocket1.send(json.dumps({
                "type": "query",
                "content": {
                    "text": "Hello, please respond with a simple greeting.",
                    "resume": False,
                    "files": []
                }
            }))
            
            # Wait for processing acknowledgment
            response = await websocket1.recv()
            data = json.loads(response)
            print(f"📥 Received: {data.get('type')}")
            
            print("✅ First connection working properly")
        
        print("\n2️⃣ Testing second connection (should reuse session)...")
        
        # Second connection with same device_id
        async with websockets.connect(uri) as websocket2:
            print("✅ Second connection established without database errors")
            
            # Initialize agent again
            await websocket2.send(json.dumps({
                "type": "init_agent",
                "content": {
                    "model_name": "gemini-2.5-flash",
                    "tool_args": {
                        "deep_research": False,
                        "pdf": True,
                        "media_generation": True,
                        "audio_generation": True,
                        "browser": True,
                    }
                }
            }))
            
            # Wait for initialization
            response = await websocket2.recv()
            data = json.loads(response)
            print(f"📥 Received: {data.get('type')}")
            
            print("✅ Second connection initialized successfully")
        
        print("\n🎉 Database constraint fix test PASSED!")
        print("✅ No UNIQUE constraint errors occurred")
        print("✅ Multiple connections handled properly")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        if "UNIQUE constraint failed" in str(e):
            print("💥 Database constraint error still exists!")
        else:
            print("🔍 Different error occurred")

if __name__ == "__main__":
    print("🚀 Database Constraint Fix Test")
    print("Make sure the WebSocket server is running on localhost:8000")
    print()
    
    asyncio.run(test_database_fix())
