"use client";

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Key, Wifi, Refresh<PERSON>w } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface ErrorAlertProps {
  error: {
    error_type: string;
    message: string;
    user_friendly_message: string;
    suggestions?: string[];
  };
  onRetry?: () => void;
  onSwitchModel?: () => void;
  className?: string;
}

const ErrorAlert: React.FC<ErrorAlertProps> = ({
  error,
  onRetry,
  onSwitchModel,
  className = "",
}) => {
  const getErrorIcon = () => {
    switch (error.error_type) {
      case "quota_exceeded":
        return <CreditCard className="h-4 w-4" />;
      case "authentication_error":
        return <Key className="h-4 w-4" />;
      case "network_error":
        return <Wifi className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getErrorColor = () => {
    switch (error.error_type) {
      case "quota_exceeded":
        return "border-orange-500 bg-orange-50 dark:bg-orange-950/20";
      case "authentication_error":
        return "border-red-500 bg-red-50 dark:bg-red-950/20";
      case "network_error":
        return "border-blue-500 bg-blue-50 dark:bg-blue-950/20";
      default:
        return "border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20";
    }
  };

  const getErrorTitle = () => {
    switch (error.error_type) {
      case "quota_exceeded":
        return "API Quota Exceeded";
      case "authentication_error":
        return "Authentication Error";
      case "network_error":
        return "Network Error";
      default:
        return "Error";
    }
  };

  const getBadgeVariant = () => {
    switch (error.error_type) {
      case "quota_exceeded":
        return "secondary";
      case "authentication_error":
        return "destructive";
      case "network_error":
        return "outline";
      default:
        return "secondary";
    }
  };

  return (
    <Alert className={`${getErrorColor()} ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-0.5">
          {getErrorIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTitle className="text-sm font-medium">
              {getErrorTitle()}
            </AlertTitle>
            <Badge variant={getBadgeVariant()} className="text-xs">
              {error.error_type.replace("_", " ")}
            </Badge>
          </div>
          
          <AlertDescription className="text-sm text-muted-foreground mb-3">
            {error.user_friendly_message}
          </AlertDescription>

          {error.suggestions && error.suggestions.length > 0 && (
            <div className="mb-3">
              <p className="text-xs font-medium text-muted-foreground mb-2">
                Suggestions:
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                {error.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="flex flex-wrap gap-2">
            {onRetry && (
              <Button
                size="sm"
                variant="outline"
                onClick={onRetry}
                className="h-7 px-3 text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            )}
            
            {onSwitchModel && (error.error_type === "quota_exceeded" || error.error_type === "authentication_error") && (
              <Button
                size="sm"
                variant="outline"
                onClick={onSwitchModel}
                className="h-7 px-3 text-xs"
              >
                Switch Model
              </Button>
            )}
          </div>

          {/* Technical details (collapsible) */}
          <details className="mt-3">
            <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
              Technical Details
            </summary>
            <div className="mt-2 p-2 bg-muted/50 rounded text-xs font-mono text-muted-foreground break-all">
              {error.message}
            </div>
          </details>
        </div>
      </div>
    </Alert>
  );
};

export default ErrorAlert;
