/* Custom Utilities */

@layer utilities {
  /* Text Utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Gradient Utilities */
  .bg-gradient-skyblue-lavender {
    background: linear-gradient(
      0deg,
      hsla(193, 65%, 83%, 1) 24%,
      hsla(284, 71%, 82%, 1) 80%
    );
  }
  
  /* Additional Gradient Presets */
  .bg-gradient-primary {
    background: linear-gradient(
      135deg,
      var(--primary) 0%,
      var(--primary-foreground) 100%
    );
  }
  
  .bg-gradient-brand {
    background: linear-gradient(
      135deg,
      var(--color-brand-primary) 0%,
      var(--color-brand-secondary) 100%
    );
  }
  
  .bg-gradient-dark {
    background: linear-gradient(
      180deg,
      var(--background) 0%,
      var(--muted) 100%
    );
  }
  
  /* Scrollbar Styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: var(--muted-foreground) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: var(--muted-foreground);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: var(--foreground);
  }
  
  /* Focus Utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
  }
  
  /* Container Utilities */
  .container-prose {
    max-width: 65ch;
    margin-left: auto;
    margin-right: auto;
  }
  
  /* Aspect Ratio Utilities */
  .aspect-video {
    aspect-ratio: 16 / 9;
  }
  
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  
  .aspect-portrait {
    aspect-ratio: 3 / 4;
  }
  
  /* Glass Morphism Effect */
  .glassmorphism {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .glassmorphism-dark {
    background: rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  /* Loading States */
  .skeleton {
    background: linear-gradient(
      90deg,
      var(--muted) 25%,
      var(--muted-foreground) 50%,
      var(--muted) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
}