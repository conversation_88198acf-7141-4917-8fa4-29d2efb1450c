#!/usr/bin/env python3
"""
Test script to verify error classification functionality.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import the classification function
sys.path.insert(0, os.path.dirname(__file__))
from ws_server import classify_api_error

def test_quota_error():
    """Test quota/rate limit error classification."""
    print("🧪 Testing quota error classification...")
    
    # Test Gemini quota error
    gemini_error = """429 RESOURCE_EXHAUSTED. {'error': {'code': 429, 'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.', 'status': 'RESOURCE_EXHAUSTED'}}"""
    
    result = classify_api_error(gemini_error)
    
    print(f"Error message: {gemini_error[:100]}...")
    print(f"Classified as: {result['error_type']}")
    print(f"User-friendly message: {result['user_friendly_message']}")
    print(f"Suggestions: {result['suggestions']}")
    
    if result['error_type'] == 'quota_exceeded':
        print("✅ Gemini quota error correctly classified")
        return True
    else:
        print("❌ Gemini quota error incorrectly classified")
        return False

def test_auth_error():
    """Test authentication error classification."""
    print("\n🧪 Testing authentication error classification...")
    
    # Test Anthropic auth error
    auth_error = """Could not resolve authentication method. Expected either api_key or auth_token to be set. Or for one of the `X-Api-Key` or `Authorization` headers to be explicitly omitted"""
    
    result = classify_api_error(auth_error)
    
    print(f"Error message: {auth_error[:100]}...")
    print(f"Classified as: {result['error_type']}")
    print(f"User-friendly message: {result['user_friendly_message']}")
    print(f"Suggestions: {result['suggestions']}")
    
    if result['error_type'] == 'authentication_error':
        print("✅ Authentication error correctly classified")
        return True
    else:
        print("❌ Authentication error incorrectly classified")
        return False

def test_network_error():
    """Test network error classification."""
    print("\n🧪 Testing network error classification...")
    
    # Test network error
    network_error = """Connection timeout: Unable to reach the server"""
    
    result = classify_api_error(network_error)
    
    print(f"Error message: {network_error}")
    print(f"Classified as: {result['error_type']}")
    print(f"User-friendly message: {result['user_friendly_message']}")
    print(f"Suggestions: {result['suggestions']}")
    
    if result['error_type'] == 'network_error':
        print("✅ Network error correctly classified")
        return True
    else:
        print("❌ Network error incorrectly classified")
        return False

def test_general_error():
    """Test general error classification."""
    print("\n🧪 Testing general error classification...")
    
    # Test general error
    general_error = """Some unexpected error occurred"""
    
    result = classify_api_error(general_error)
    
    print(f"Error message: {general_error}")
    print(f"Classified as: {result['error_type']}")
    print(f"User-friendly message: {result['user_friendly_message']}")
    print(f"Suggestions: {result['suggestions']}")
    
    if result['error_type'] == 'general_error':
        print("✅ General error correctly classified")
        return True
    else:
        print("❌ General error incorrectly classified")
        return False

def main():
    """Run all error classification tests."""
    print("🚀 Starting Error Classification Tests\n")
    
    results = []
    results.append(test_quota_error())
    results.append(test_auth_error())
    results.append(test_network_error())
    results.append(test_general_error())
    
    print(f"\n{'='*60}")
    print(f"📋 Test Summary:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print(f"\n🎉 All tests passed! Error classification is working correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Check the errors above.")
    
    return all(results)

if __name__ == "__main__":
    main()
