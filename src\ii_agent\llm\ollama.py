"""LLM client for Ollama models."""

import json
import os
import random
import time
from typing import <PERSON>, <PERSON><PERSON>, cast
import ollama

from ii_agent.llm.base import (
    LLMClient,
    AssistantContentBlock,
    LLMMessages,
    ToolParam,
    TextPrompt,
    ToolCall,
    TextResult,
    ToolFormattedResult,
    UserContentBlock,
    ImageBlock,
)


class OllamaDirectClient(LLMClient):
    """Use Ollama models via local API."""

    def __init__(self, model_name: str, max_retries=2, host: str = None):
        """Initialize the Ollama client."""
        self.model_name = model_name
        self.max_retries = max_retries
        
        # Configure Ollama host
        if host:
            self.host = host
        else:
            self.host = os.getenv("OLLAMA_HOST", "http://localhost:11434")
        
        # Initialize Ollama client
        self.client = ollama.Client(host=self.host)
        
        print(f"====== Using Ollama model '{model_name}' at {self.host} ======")
        
        # Test connection and model availability
        try:
            models = self.client.list()
            # Handle both old dict format and new object format
            if hasattr(models, 'models'):
                available_models = [model.model for model in models.models]
            else:
                available_models = [model.get('name', model.get('model', '')) for model in models.get('models', [])]

            if model_name not in available_models:
                print(f"Warning: Model '{model_name}' not found in available models: {available_models}")
                print(f"You may need to pull the model first: ollama pull {model_name}")
        except Exception as e:
            print(f"Warning: Could not connect to Ollama at {self.host}: {str(e)}")

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.

        Returns:
            A generated response.
        """
        assert thinking_tokens is None, "Thinking tokens not implemented for Ollama"
        
        # Convert internal messages to Ollama format
        ollama_messages = []
        
        for message_list in messages:
            role = "user" if isinstance(message_list[0], UserContentBlock) else "assistant"
            
            # Combine all content blocks into a single message
            content_parts = []
            
            for content_block in message_list:
                if isinstance(content_block, TextPrompt):
                    content_parts.append(content_block.text)
                elif isinstance(content_block, TextResult):
                    content_parts.append(content_block.text)
                elif isinstance(content_block, ToolCall):
                    # Format tool calls for Ollama
                    tool_call_text = f"[Tool Call: {content_block.name}]\n"
                    tool_call_text += f"Arguments: {json.dumps(content_block.arguments)}\n"
                    content_parts.append(tool_call_text)
                elif isinstance(content_block, ToolFormattedResult):
                    # Format tool results for Ollama
                    tool_result_text = f"[Tool Result: {content_block.tool_name}]\n"
                    tool_result_text += f"Result: {content_block.result}\n"
                    content_parts.append(tool_result_text)
                elif isinstance(content_block, ImageBlock):
                    # Ollama supports images, but we'll skip for now
                    content_parts.append("[Image content - not supported in this implementation]")
            
            if content_parts:
                ollama_messages.append({
                    "role": role,
                    "content": "\n".join(content_parts)
                })
        
        # Add system prompt if provided
        if system_prompt:
            ollama_messages.insert(0, {
                "role": "system", 
                "content": system_prompt
            })
        
        # Note: Ollama doesn't have native tool calling support like OpenAI/Anthropic
        # We'll handle tools by including their descriptions in the system prompt
        if tools:
            tool_descriptions = []
            for tool in tools:
                # tool is a ToolParam object, not a dictionary
                tool_desc = f"Tool: {tool.name}\n"
                tool_desc += f"Description: {tool.description}\n"
                if tool.input_schema:
                    tool_desc += f"Parameters: {json.dumps(tool.input_schema)}\n"
                tool_descriptions.append(tool_desc)
            
            tool_prompt = "\n\nAvailable Tools:\n" + "\n".join(tool_descriptions)
            tool_prompt += "\n\nTo use a tool, respond with: [Tool Call: tool_name] followed by the arguments in JSON format."
            
            if system_prompt:
                ollama_messages[0]["content"] += tool_prompt
            else:
                ollama_messages.insert(0, {
                    "role": "system",
                    "content": "You are a helpful assistant." + tool_prompt
                })

        response = None
        for retry in range(self.max_retries):
            try:
                response = self.client.chat(
                    model=self.model_name,
                    messages=ollama_messages,
                    options={
                        "temperature": temperature,
                        "num_predict": max_tokens,
                    }
                )
                break
            except Exception as e:
                if retry == self.max_retries - 1:
                    print(f"Failed Ollama request after {retry + 1} retries")
                    raise e
                else:
                    print(f"Retrying Ollama request: {retry + 1}/{self.max_retries}")
                    # Sleep 2-4 seconds with jitter
                    time.sleep(3 * random.uniform(0.8, 1.2))

        # Convert response back to internal format
        internal_messages = []
        assert response is not None
        
        response_content = response.get('message', {}).get('content', '')
        
        # Simple tool call detection (this is a basic implementation)
        if "[Tool Call:" in response_content and tools:
            # Try to parse tool calls from the response
            lines = response_content.split('\n')
            current_tool_call = None
            
            for line in lines:
                if line.startswith("[Tool Call:"):
                    # Extract tool name
                    tool_name = line.replace("[Tool Call:", "").replace("]", "").strip()
                    current_tool_call = {"name": tool_name, "arguments": {}}
                elif current_tool_call and line.strip().startswith("{"):
                    # Try to parse JSON arguments
                    try:
                        current_tool_call["arguments"] = json.loads(line.strip())
                        internal_messages.append(ToolCall(
                            name=current_tool_call["name"],
                            arguments=current_tool_call["arguments"]
                        ))
                        current_tool_call = None
                    except json.JSONDecodeError:
                        # If JSON parsing fails, treat as regular text
                        pass
            
            # If no valid tool calls were parsed, treat as regular text
            if not internal_messages:
                internal_messages.append(TextResult(text=response_content))
        else:
            internal_messages.append(TextResult(text=response_content))

        # Return response and metadata
        # Safely handle token counts that might be None
        eval_count = response.get('eval_count') or 0
        prompt_eval_count = response.get('prompt_eval_count') or 0

        metadata = {
            "model": self.model_name,
            "total_tokens": eval_count + prompt_eval_count,
            "prompt_tokens": prompt_eval_count,
            "completion_tokens": eval_count,
        }

        return internal_messages, metadata
