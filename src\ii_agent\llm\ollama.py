"""LLM client for Ollama models."""

import json
import os
import random
import time
from typing import <PERSON>, <PERSON><PERSON>, cast
import ollama

from ii_agent.llm.base import (
    LLMClient,
    AssistantContentBlock,
    LLMMessages,
    ToolParam,
    TextPrompt,
    ToolCall,
    TextResult,
    ToolFormattedResult,
    UserContentBlock,
    ImageBlock,
)


class OllamaDirectClient(LLMClient):
    """Use Ollama models via local API."""

    def __init__(self, model_name: str, max_retries=2, host: str = None):
        """Initialize the Ollama client."""
        self.model_name = model_name
        self.max_retries = max_retries
        
        # Configure Ollama host
        if host:
            self.host = host
        else:
            self.host = os.getenv("OLLAMA_HOST", "http://localhost:11434")
        
        # Initialize Ollama client
        self.client = ollama.Client(host=self.host)
        
        print(f"====== Using Ollama model '{model_name}' at {self.host} ======")
        
        # Test connection and model availability
        try:
            models = self.client.list()
            # Handle both old dict format and new object format
            if hasattr(models, 'models'):
                available_models = [model.model for model in models.models]
            else:
                available_models = [model.get('name', model.get('model', '')) for model in models.get('models', [])]

            if model_name not in available_models:
                print(f"Warning: Model '{model_name}' not found in available models: {available_models}")
                print(f"You may need to pull the model first: ollama pull {model_name}")
        except Exception as e:
            print(f"Warning: Could not connect to Ollama at {self.host}: {str(e)}")

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.

        Returns:
            A generated response.
        """
        assert thinking_tokens is None, "Thinking tokens not implemented for Ollama"

        print(f"🚀 [Ollama] Starting generation with model: {self.model_name}")
        print(f"📝 [Ollama] Input messages count: {len(messages)}")
        print(f"🌡️ [Ollama] Temperature: {temperature}")
        print(f"🔧 [Ollama] Tools available: {len(tools)}")
        print(f"💭 [Ollama] System prompt: {'Yes' if system_prompt else 'No'}")
        print(f"🎯 [Ollama] Max tokens: {max_tokens}")

        # Log the last user message for debugging
        if messages:
            last_message = messages[-1]
            if last_message:
                last_content = ""
                for content_block in last_message:
                    if hasattr(content_block, 'text'):
                        last_content += content_block.text + " "
                content_preview = last_content[:100] + "..." if len(last_content) > 100 else last_content
                print(f"💬 [Ollama] Last user message preview: {content_preview}")
            else:
                print(f"💬 [Ollama] Last message is empty")
        else:
            print(f"⚠️ [Ollama] No messages provided!")

        # Convert internal messages to Ollama format
        ollama_messages = []
        
        for message_list in messages:
            role = "user" if isinstance(message_list[0], UserContentBlock) else "assistant"
            
            # Combine all content blocks into a single message
            content_parts = []
            
            for content_block in message_list:
                if isinstance(content_block, TextPrompt):
                    content_parts.append(content_block.text)
                elif isinstance(content_block, TextResult):
                    content_parts.append(content_block.text)
                elif isinstance(content_block, ToolCall):
                    # Format tool calls for Ollama
                    tool_call_text = f"[Tool Call: {content_block.name}]\n"
                    tool_call_text += f"Arguments: {json.dumps(content_block.arguments)}\n"
                    content_parts.append(tool_call_text)
                elif isinstance(content_block, ToolFormattedResult):
                    # Format tool results for Ollama
                    tool_result_text = f"[Tool Result: {content_block.tool_name}]\n"
                    tool_result_text += f"Result: {content_block.result}\n"
                    content_parts.append(tool_result_text)
                elif isinstance(content_block, ImageBlock):
                    # Ollama supports images, but we'll skip for now
                    content_parts.append("[Image content - not supported in this implementation]")
            
            if content_parts:
                ollama_messages.append({
                    "role": role,
                    "content": "\n".join(content_parts)
                })
        
        # Add system prompt if provided
        if system_prompt:
            ollama_messages.insert(0, {
                "role": "system", 
                "content": system_prompt
            })
        
        # Note: Ollama doesn't have native tool calling support like OpenAI/Anthropic
        # We'll handle tools by including their descriptions in the system prompt
        if tools:
            print(f"🔧 [Ollama] Processing {len(tools)} tools for system prompt")
            tool_descriptions = []
            for i, tool in enumerate(tools):
                # tool is a ToolParam object, not a dictionary
                tool_desc = f"Tool: {tool.name}\n"
                tool_desc += f"Description: {tool.description}\n"
                if tool.input_schema:
                    tool_desc += f"Parameters: {json.dumps(tool.input_schema)}\n"
                tool_descriptions.append(tool_desc)
                print(f"🛠️ [Ollama] Tool {i+1}: {tool.name} - {tool.description[:50]}...")

            tool_prompt = "\n\nAvailable Tools:\n" + "\n".join(tool_descriptions)
            tool_prompt += "\n\nIMPORTANT: When you need to use a tool, you MUST respond with the exact format:"
            tool_prompt += "\n[Tool Call: tool_name]"
            tool_prompt += "\n{\"parameter\": \"value\"}"
            tool_prompt += "\n\nFor example, to search the web:"
            tool_prompt += "\n[Tool Call: web_search]"
            tool_prompt += "\n{\"query\": \"AI projects 2024\"}"
            tool_prompt += "\n\nDo NOT use <think> tags or other formats. Use tools when you need to search, browse, or perform actions."
            tool_prompt += "\nFor the user's request about searching AI projects, you should use the web_search tool first."
            tool_prompt += "\n\nREMEMBER: You MUST use tools to get current information. Do NOT generate content from memory."
            tool_prompt += "\nIf the user asks for search or current information, start with [Tool Call: web_search]"

            print(f"📋 [Ollama] Tool prompt length: {len(tool_prompt)} characters")

            if system_prompt:
                ollama_messages[0]["content"] += tool_prompt
                print(f"✅ [Ollama] Added tools to existing system prompt")
            else:
                ollama_messages.insert(0, {
                    "role": "system",
                    "content": "You are a helpful assistant." + tool_prompt
                })
                print(f"✅ [Ollama] Created new system prompt with tools")
        else:
            print(f"⚠️ [Ollama] No tools provided!")

        print(f"📤 [Ollama] Sending request to Ollama...")
        print(f"📋 [Ollama] Ollama messages count: {len(ollama_messages)}")

        # Log the final message structure (first 200 chars of each message)
        for i, msg in enumerate(ollama_messages):
            content_preview = msg.get('content', '')[:200] + "..." if len(msg.get('content', '')) > 200 else msg.get('content', '')
            print(f"📨 [Ollama] Message {i+1} ({msg.get('role', 'unknown')}): {content_preview}")

        response = None
        start_time = time.time()

        for retry in range(self.max_retries):
            try:
                print(f"🔄 [Ollama] Attempt {retry + 1}/{self.max_retries} - Making API call...")
                response = self.client.chat(
                    model=self.model_name,
                    messages=ollama_messages,
                    options={
                        "temperature": temperature,
                        "num_predict": max_tokens,
                    }
                )
                elapsed_time = time.time() - start_time
                print(f"✅ [Ollama] Request successful in {elapsed_time:.2f}s")
                break
            except Exception as e:
                elapsed_time = time.time() - start_time
                print(f"❌ [Ollama] Request failed after {elapsed_time:.2f}s: {str(e)}")
                if retry == self.max_retries - 1:
                    print(f"💥 [Ollama] Failed Ollama request after {retry + 1} retries")
                    raise e
                else:
                    print(f"🔄 [Ollama] Retrying Ollama request: {retry + 1}/{self.max_retries}")
                    # Sleep 2-4 seconds with jitter
                    time.sleep(3 * random.uniform(0.8, 1.2))

        # Convert response back to internal format
        internal_messages = []
        assert response is not None

        print(f"📥 [Ollama] Processing response...")
        print(f"🔍 [Ollama] Raw response type: {type(response)}")

        # Handle ChatResponse object properly (it's a Pydantic model)
        if hasattr(response, 'message'):
            message = response.message
            response_content = message.content if hasattr(message, 'content') else str(message)
            print(f"✅ [Ollama] Extracted content from message.content")
        else:
            response_content = str(response)
            print(f"⚠️ [Ollama] No message attribute, using str(response)")

        print(f"📝 [Ollama] Response content length: {len(response_content)}")

        # Log response content preview
        content_preview = response_content[:300] + "..." if len(response_content) > 300 else response_content
        print(f"💬 [Ollama] Response content preview: {content_preview}")
        
        # Enhanced tool call detection
        print(f"🔍 [Ollama] Checking for tool calls in response...")
        print(f"🔍 [Ollama] Looking for '[Tool Call:' pattern...")

        # Also check for alternative patterns that models might use
        has_tool_call = "[Tool Call:" in response_content
        has_search_intent = any(keyword in response_content.lower() for keyword in ["搜索", "搜尋", "search", "查找", "查詢"])

        print(f"🔍 [Ollama] Tool call pattern found: {has_tool_call}")
        print(f"🔍 [Ollama] Search intent detected: {has_search_intent}")

        if has_tool_call and tools:
            print(f"✅ [Ollama] Found '[Tool Call:' pattern in response!")
            # Try to parse tool calls from the response
            lines = response_content.split('\n')
            current_tool_call = None

            for line_num, line in enumerate(lines):
                print(f"🔍 [Ollama] Line {line_num}: {line[:100]}...")

                if "[Tool Call:" in line:
                    # Extract tool name - more flexible parsing
                    start_idx = line.find("[Tool Call:") + len("[Tool Call:")
                    end_idx = line.find("]", start_idx)
                    if end_idx != -1:
                        tool_name = line[start_idx:end_idx].strip()
                    else:
                        tool_name = line[start_idx:].strip()

                    current_tool_call = {"name": tool_name, "arguments": {}}
                    print(f"🛠️ [Ollama] Found tool call: {tool_name}")

                elif current_tool_call and line.strip().startswith("{"):
                    # Try to parse JSON arguments
                    try:
                        current_tool_call["arguments"] = json.loads(line.strip())
                        internal_messages.append(ToolCall(
                            name=current_tool_call["name"],
                            arguments=current_tool_call["arguments"]
                        ))
                        print(f"✅ [Ollama] Successfully parsed tool call: {current_tool_call['name']}")
                        current_tool_call = None
                    except json.JSONDecodeError as e:
                        print(f"❌ [Ollama] JSON parsing failed: {e}")
                        # If JSON parsing fails, treat as regular text
                        pass

            # If no valid tool calls were parsed, treat as regular text
            if not internal_messages:
                print(f"⚠️ [Ollama] No valid tool calls parsed, treating as text")
                internal_messages.append(TextResult(text=response_content))
        else:
            if tools:
                print(f"❌ [Ollama] No '[Tool Call:' pattern found in response")
                print(f"🔍 [Ollama] Response contains: {response_content[:200]}...")
            else:
                print(f"ℹ️ [Ollama] No tools available, treating as text")
            internal_messages.append(TextResult(text=response_content))

        # Return response and metadata
        # Safely handle token counts that might be None
        eval_count = 0
        prompt_eval_count = 0

        # Try to get token counts from ChatResponse object
        if hasattr(response, 'eval_count'):
            eval_count = response.eval_count or 0
        if hasattr(response, 'prompt_eval_count'):
            prompt_eval_count = response.prompt_eval_count or 0

        # Fallback: try to access as dict if it's still dict-like
        if eval_count == 0 and hasattr(response, 'get'):
            eval_count = response.get('eval_count', 0) or 0
        if prompt_eval_count == 0 and hasattr(response, 'get'):
            prompt_eval_count = response.get('prompt_eval_count', 0) or 0

        metadata = {
            "model": self.model_name,
            "total_tokens": eval_count + prompt_eval_count,
            "prompt_tokens": prompt_eval_count,
            "completion_tokens": eval_count,
        }

        print(f"📊 [Ollama] Token usage - Prompt: {prompt_eval_count}, Completion: {eval_count}, Total: {eval_count + prompt_eval_count}")
        print(f"🎯 [Ollama] Generated {len(internal_messages)} internal message(s)")

        # Log the types of internal messages generated
        for i, msg in enumerate(internal_messages):
            print(f"📋 [Ollama] Internal message {i+1}: {type(msg).__name__}")

        print(f"🏁 [Ollama] Generation completed successfully!")

        return internal_messages, metadata
