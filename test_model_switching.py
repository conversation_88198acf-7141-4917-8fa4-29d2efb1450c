#!/usr/bin/env python3
"""
Test script for model switching functionality.
This script tests the ability to switch models during task execution and continue with the remaining tasks.
"""

import asyncio
import json
import websockets
import uuid
from typing import Dict, Any

# Test configuration
WEBSOCKET_URL = "ws://localhost:8000/ws"
TEST_DEVICE_ID = str(uuid.uuid4())

class ModelSwitchingTest:
    def __init__(self):
        self.websocket = None
        self.messages_received = []
        
    async def connect(self):
        """Connect to the WebSocket server."""
        try:
            self.websocket = await websockets.connect(
                f"{WEBSOCKET_URL}?device_id={TEST_DEVICE_ID}"
            )
            print("✅ Connected to WebSocket server")
            return True
        except Exception as e:
            print(f"❌ Failed to connect: {e}")
            return False
    
    async def send_message(self, message_type: str, content: Dict[str, Any]):
        """Send a message to the server."""
        if not self.websocket:
            print("❌ Not connected to WebSocket")
            return False
            
        message = {
            "type": message_type,
            "content": content
        }
        
        try:
            await self.websocket.send(json.dumps(message))
            print(f"📤 Sent: {message_type}")
            return True
        except Exception as e:
            print(f"❌ Failed to send message: {e}")
            return False
    
    async def receive_messages(self, timeout=5):
        """Receive messages from the server."""
        try:
            message = await asyncio.wait_for(
                self.websocket.recv(), 
                timeout=timeout
            )
            data = json.loads(message)
            self.messages_received.append(data)
            print(f"📥 Received: {data.get('type', 'unknown')} - {data.get('content', {}).get('message', '')}")
            return data
        except asyncio.TimeoutError:
            print("⏰ Timeout waiting for message")
            return None
        except Exception as e:
            print(f"❌ Error receiving message: {e}")
            return None
    
    async def test_model_switching_workflow(self):
        """Test the complete model switching workflow."""
        print("\n🧪 Starting Model Switching Test")
        print("=" * 50)
        
        # Step 1: Initialize agent with first model
        print("\n1️⃣ Initializing agent with Gemini model...")
        await self.send_message("init_agent", {
            "model_name": "gemini-2.5-flash",
            "tool_args": {
                "deep_research": False,
                "pdf": True,
                "media_generation": True,
                "audio_generation": True,
                "browser": True,
            }
        })
        
        # Wait for initialization response
        response = await self.receive_messages()
        if not response or response.get("type") != "agent_initialized":
            print("❌ Failed to initialize agent")
            return False
        
        # Step 2: Send a complex task that might fail due to quota
        print("\n2️⃣ Sending a complex task...")
        await self.send_message("query", {
            "text": "Please help me create a simple Python script that reads a CSV file and generates a bar chart. Create the files in the workspace.",
            "resume": False,
            "files": []
        })
        
        # Wait for processing acknowledgment
        response = await self.receive_messages()
        if response and response.get("type") == "processing":
            print("✅ Task processing started")
        
        # Step 3: Simulate quota error (in real scenario, this would come from API)
        print("\n3️⃣ Simulating quota exhaustion...")
        # In a real test, we would wait for an actual quota error
        # For this test, we'll simulate switching models
        
        # Step 4: Switch to a different model
        print("\n4️⃣ Switching to Ollama model...")
        await self.send_message("init_agent", {
            "model_name": "ollama:llama3.2",
            "tool_args": {
                "deep_research": False,
                "pdf": True,
                "media_generation": True,
                "audio_generation": True,
                "browser": True,
            }
        })
        
        # Wait for re-initialization response
        response = await self.receive_messages()
        if not response or response.get("type") != "agent_initialized":
            print("❌ Failed to re-initialize agent with new model")
            return False
        
        print("✅ Successfully switched to new model")
        
        # Step 5: Continue with the task using resume=True
        print("\n5️⃣ Continuing task with new model...")
        await self.send_message("query", {
            "text": "Please help me create a simple Python script that reads a CSV file and generates a bar chart. Create the files in the workspace.",
            "resume": True,  # This is key - continue from where we left off
            "files": []
        })
        
        # Wait for processing acknowledgment
        response = await self.receive_messages()
        if response and response.get("type") == "processing":
            print("✅ Task continuation started with new model")
        
        # Step 6: Monitor for completion or further messages
        print("\n6️⃣ Monitoring task execution...")
        message_count = 0
        max_messages = 10  # Limit to avoid infinite loop
        
        while message_count < max_messages:
            response = await self.receive_messages(timeout=10)
            if not response:
                break
                
            message_count += 1
            
            # Check for completion or error
            if response.get("type") == "agent_response":
                print("✅ Task completed successfully!")
                break
            elif response.get("type") == "error":
                error_content = response.get("content", {})
                print(f"❌ Error occurred: {error_content.get('message', 'Unknown error')}")
                break
        
        print(f"\n📊 Test completed. Received {len(self.messages_received)} messages total.")
        return True
    
    async def close(self):
        """Close the WebSocket connection."""
        if self.websocket:
            await self.websocket.close()
            print("🔌 Disconnected from WebSocket server")

async def main():
    """Main test function."""
    print("🚀 Model Switching Test Suite")
    print("Testing the ability to switch models and continue tasks...")
    
    test = ModelSwitchingTest()
    
    try:
        # Connect to server
        if not await test.connect():
            return
        
        # Run the test workflow
        await test.test_model_switching_workflow()
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
    finally:
        await test.close()

if __name__ == "__main__":
    print("📋 Prerequisites:")
    print("   1. WebSocket server should be running on localhost:8000")
    print("   2. At least one model should be available (Gemini or Ollama)")
    print("   3. Backend should support model switching functionality")
    print()
    
    asyncio.run(main())
