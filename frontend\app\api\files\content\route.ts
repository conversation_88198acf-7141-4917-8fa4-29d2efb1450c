import { NextResponse } from "next/server";
import fs from "fs/promises";

export async function POST(request: Request) {
  try {
    const { path } = await request.json();
    if (!path) {
      return NextResponse.json({ error: "Path is required" }, { status: 400 });
    }

    // 確保以 UTF-8 編碼讀取文件
    const content = await fs.readFile(path, "utf-8");

    // 設置正確的響應頭以支持 UTF-8
    const response = NextResponse.json({ content });
    response.headers.set('Content-Type', 'application/json; charset=utf-8');

    return response;
  } catch (error) {
    console.error("Error reading file:", error);
    return NextResponse.json({ error: "Failed to read file" }, { status: 500 });
  }
}
