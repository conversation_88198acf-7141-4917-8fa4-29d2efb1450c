#!/usr/bin/env python3
"""
Debug script to test API endpoints and diagnose issues.
"""

import requests
import json
import sys
from pathlib import Path

def test_health_check(base_url):
    """Test the health check endpoint."""
    print("🔍 Testing health check endpoint...")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        if response.status_code == 200:
            print(f"✅ Health check passed: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code} - {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_sessions_endpoint(base_url, device_id="test-device"):
    """Test the sessions endpoint."""
    print(f"🔍 Testing sessions endpoint with device_id: {device_id}...")
    try:
        response = requests.get(f"{base_url}/api/sessions/{device_id}", timeout=10)
        if response.status_code == 200:
            data = response.json()
            sessions = data.get("sessions", [])
            print(f"✅ Sessions endpoint working: Found {len(sessions)} sessions")
            if sessions:
                print(f"📋 Sample session: {sessions[0]}")
            return True
        else:
            print(f"❌ Sessions endpoint failed: {response.status_code} - {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Sessions endpoint failed: {e}")
        return False

def test_database_connection():
    """Test database connection."""
    print("🔍 Testing database connection...")
    try:
        # Add the src directory to the path
        sys.path.insert(0, 'src')
        
        from ii_agent.db.manager import DatabaseManager
        
        db_manager = DatabaseManager()
        with db_manager.get_session() as session:
            from ii_agent.db.models import Session
            count = session.query(Session).count()
            print(f"✅ Database connection working: Found {count} total sessions")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def check_environment():
    """Check environment setup."""
    print("🔍 Checking environment setup...")
    
    # Check if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")
    else:
        print("⚠️  .env file not found")
    
    # Check frontend .env.local
    frontend_env = Path("frontend/.env.local")
    if frontend_env.exists():
        print("✅ frontend/.env.local file found")
        with open(frontend_env, 'r') as f:
            content = f.read()
            if "NEXT_PUBLIC_API_URL" in content:
                print("✅ NEXT_PUBLIC_API_URL configured")
            else:
                print("❌ NEXT_PUBLIC_API_URL not found in frontend/.env.local")
    else:
        print("❌ frontend/.env.local file not found")
    
    # Check if database file exists
    db_file = Path("agent.db")
    if db_file.exists():
        print(f"✅ Database file found: {db_file.stat().st_size} bytes")
    else:
        print("⚠️  Database file not found (will be created on first use)")

def main():
    """Run all diagnostic tests."""
    print("🚀 Starting II-Agent API Diagnostics\n")
    
    base_url = "http://localhost:8000"
    
    results = []
    
    # Check environment
    check_environment()
    print()
    
    # Test database connection
    results.append(test_database_connection())
    print()
    
    # Test health check
    results.append(test_health_check(base_url))
    print()
    
    # Test sessions endpoint
    results.append(test_sessions_endpoint(base_url))
    print()
    
    # Summary
    print("="*50)
    print("📋 Diagnostic Summary:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print("\n🎉 All tests passed! The API should be working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
        print("\n💡 Troubleshooting Tips:")
        print("   1. Make sure the WebSocket server is running: python ws_server.py")
        print("   2. Check that the correct port (8000) is being used")
        print("   3. Verify environment variables are set correctly")
        print("   4. Check if any firewall is blocking the connection")

if __name__ == "__main__":
    main()
