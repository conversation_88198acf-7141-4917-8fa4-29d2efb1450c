#!/usr/bin/env python3
"""
Simple test for model switching functionality.
"""

import asyncio
import json
import websockets
import uuid

async def test_model_switch():
    device_id = str(uuid.uuid4())
    uri = f"ws://localhost:8000/ws?device_id={device_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket")
            
            # Step 1: Initialize with first model
            print("🔄 Initializing with Gemini model...")
            await websocket.send(json.dumps({
                "type": "init_agent",
                "content": {
                    "model_name": "gemini-2.5-flash",
                    "tool_args": {
                        "deep_research": False,
                        "pdf": True,
                        "media_generation": True,
                        "audio_generation": True,
                        "browser": True,
                    }
                }
            }))
            
            # Wait for initialization
            response = await websocket.recv()
            data = json.loads(response)
            print(f"📥 Received: {data.get('type')} - {data.get('content', {}).get('message', '')}")
            
            if data.get('type') == 'agent_initialized':
                print("✅ Agent initialized successfully")
            
            # Step 2: Send a query
            print("📤 Sending query...")
            await websocket.send(json.dumps({
                "type": "query",
                "content": {
                    "text": "Hello! Please respond with a simple greeting and tell me what model you are.",
                    "resume": False,
                    "files": []
                }
            }))
            
            # Wait for processing acknowledgment
            response = await websocket.recv()
            data = json.loads(response)
            print(f"📥 Received: {data.get('type')} - {data.get('content', {}).get('message', '')}")
            
            # Step 3: Switch to different model
            print("🔄 Switching to Ollama model...")
            await websocket.send(json.dumps({
                "type": "init_agent",
                "content": {
                    "model_name": "ollama:llama3.2",
                    "tool_args": {
                        "deep_research": False,
                        "pdf": True,
                        "media_generation": True,
                        "audio_generation": True,
                        "browser": True,
                    }
                }
            }))
            
            # Wait for re-initialization
            response = await websocket.recv()
            data = json.loads(response)
            print(f"📥 Received: {data.get('type')} - {data.get('content', {}).get('message', '')}")
            
            if data.get('type') == 'agent_initialized':
                print("✅ Agent re-initialized with new model")
                
                # Step 4: Continue with resume=true
                print("📤 Continuing task with new model...")
                await websocket.send(json.dumps({
                    "type": "query",
                    "content": {
                        "text": "Hello! Please respond with a simple greeting and tell me what model you are.",
                        "resume": True,
                        "files": []
                    }
                }))
                
                # Monitor responses
                for i in range(5):  # Listen for a few messages
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=10)
                        data = json.loads(response)
                        print(f"📥 Received: {data.get('type')} - {data.get('content', {}).get('message', '')}")
                        
                        if data.get('type') == 'agent_response':
                            print("✅ Task completed successfully with new model!")
                            break
                    except asyncio.TimeoutError:
                        print("⏰ Timeout waiting for response")
                        break
            
            print("🎉 Model switching test completed!")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    print("🧪 Simple Model Switching Test")
    print("Make sure the WebSocket server is running on localhost:8000")
    print("And that both Gemini and Ollama models are available")
    print()
    
    asyncio.run(test_model_switch())
