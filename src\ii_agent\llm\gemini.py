import os
import time
import random
import json
import re

from typing import Any, <PERSON><PERSON>
from google import genai
from google.genai import types, errors
from ii_agent.llm.base import (
    LLMClient,
    AssistantContentBlock,
    ToolParam,
    TextPrompt,
    ToolCall,
    TextResult,
    LLMMessages,
    ToolFormattedResult,
    ImageBlock,
)

def generate_tool_call_id() -> str:
    """Generate a unique ID for a tool call.

    Returns:
        A unique string ID combining timestamp and random number.
    """
    timestamp = int(time.time() * 1000)  # Current time in milliseconds
    random_num = random.randint(1000, 9999)  # Random 4-digit number
    return f"call_{timestamp}_{random_num}"


def parse_retry_delay(error_message: str) -> int:
    """Parse retryDelay from Gemini API error message.

    Args:
        error_message: The error message string containing retryDelay

    Returns:
        int: Retry delay in seconds, defaults to 60 if not found
    """
    try:
        # Look for retryDelay pattern in the error message
        # Example: 'retryDelay': '56s'
        retry_delay_match = re.search(r"'retryDelay':\s*'(\d+)s'", error_message)
        if retry_delay_match:
            return int(retry_delay_match.group(1))

        # Alternative pattern: "retryDelay": "56s"
        retry_delay_match = re.search(r'"retryDelay":\s*"(\d+)s"', error_message)
        if retry_delay_match:
            return int(retry_delay_match.group(1))

        # Try to parse as JSON if the error message contains JSON
        if '{' in error_message and '}' in error_message:
            try:
                # Extract JSON part from error message
                json_start = error_message.find('{')
                json_end = error_message.rfind('}') + 1
                json_str = error_message[json_start:json_end]
                error_data = json.loads(json_str)

                # Look for retryDelay in details
                if 'error' in error_data and 'details' in error_data['error']:
                    for detail in error_data['error']['details']:
                        if '@type' in detail and 'RetryInfo' in detail['@type']:
                            retry_delay = detail.get('retryDelay', '60s')
                            # Extract number from string like "56s"
                            delay_match = re.search(r'(\d+)', retry_delay)
                            if delay_match:
                                return int(delay_match.group(1))
            except (json.JSONDecodeError, KeyError):
                pass

    except Exception:
        pass

    # Default to 60 seconds if parsing fails
    return 60


def wait_with_progress(delay_seconds: int, reason: str = "API quota exhausted"):
    """Wait for specified time with progress logging.

    Args:
        delay_seconds: Number of seconds to wait
        reason: Reason for waiting
    """
    print(f"⏳ [Gemini] {reason} - Waiting {delay_seconds} seconds as requested by API...")

    # Show progress every 10 seconds for long waits
    if delay_seconds > 10:
        for i in range(0, delay_seconds, 10):
            remaining = delay_seconds - i
            if remaining >= 10:
                print(f"⏳ [Gemini] Still waiting... {remaining} seconds remaining")
                time.sleep(10)
            else:
                print(f"⏳ [Gemini] Almost done... {remaining} seconds remaining")
                time.sleep(remaining)
                break
    else:
        time.sleep(delay_seconds)

    print(f"✅ [Gemini] Wait completed. Retrying request...")


class GeminiDirectClient(LLMClient):
    """Use Gemini models via first party API."""

    def __init__(self, model_name: str, max_retries: int = 2, project_id: None | str = None, region: None | str = None):
        self.model_name = model_name

        if project_id and region:
            self.client = genai.Client(vertexai=True, project=project_id, location=region)
            print(f"====== Using Gemini through Vertex AI API with project_id: {project_id} and region: {region} ======")
        else:
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                raise ValueError("GEMINI_API_KEY is not set")
            self.client = genai.Client(api_key=api_key)
            print(f"====== Using Gemini directly ======")
            
        self.max_retries = max_retries

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        
        gemini_messages = []
        for idx, message_list in enumerate(messages):
            role = "user" if idx % 2 == 0 else "model"
            message_content_list = []
            for message in message_list:
                if isinstance(message, TextPrompt):
                    message_content = types.Part(text=message.text)
                elif isinstance(message, ImageBlock):
                    message_content = types.Part.from_bytes(
                            data=message.source["data"],
                            mime_type=message.source["media_type"],
                        )
                elif isinstance(message, TextResult):
                    message_content = types.Part(text=message.text)
                elif isinstance(message, ToolCall):
                    message_content = types.Part.from_function_call(
                        name=message.tool_name,
                        args=message.tool_input,
                    )
                elif isinstance(message, ToolFormattedResult):
                    if isinstance(message.tool_output, str):
                        message_content = types.Part.from_function_response(
                            name=message.tool_name,
                            response={"result": message.tool_output}
                        )
                    # Handle tool return images. See: https://discuss.ai.google.dev/t/returning-images-from-function-calls/3166/6
                    elif isinstance(message.tool_output, list):
                        message_content = []
                        for item in message.tool_output:
                            if item['type'] == 'text':
                                message_content.append(types.Part(text=item['text']))
                            elif item['type'] == 'image':
                                message_content.append(types.Part.from_bytes(
                                    data=item['source']['data'],
                                    mime_type=item['source']['media_type']
                                ))
                else:
                    raise ValueError(f"Unknown message type: {type(message)}")
                
                if isinstance(message_content, list):
                    message_content_list.extend(message_content)
                else:
                    message_content_list.append(message_content)
            
            gemini_messages.append(types.Content(role=role, parts=message_content_list))
        
        tool_params = []
        for tool in tools:
            tool_params.append(
                {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.input_schema,
                }
            )
        tool_params = types.Tool(function_declarations=tool_params)

        mode = None
        if not tool_choice:
            mode = 'ANY' # This mode always requires a tool call
        elif tool_choice['type'] == 'any':
            mode = 'ANY'
        elif tool_choice['type'] == 'auto':
            mode = 'AUTO'
        else:
            raise ValueError(f"Unknown tool_choice type for Gemini: {tool_choice['type']}")

        for retry in range(self.max_retries):
            try:
                response = self.client.models.generate_content(
                    model=self.model_name,
                    config=types.GenerateContentConfig(
                        tools=[tool_params],
                        system_instruction=system_prompt,
                        temperature=temperature,
                        max_output_tokens=max_tokens,
                        tool_config={'function_calling_config': {'mode': mode}}
                        ),
                    contents=gemini_messages,
                )
                break
            except errors.APIError as e:
                # 503: The service may be temporarily overloaded or down.
                # 429: The request was throttled.
                if e.code in [503, 429]:
                    if retry == self.max_retries - 1:
                        print(f"Failed Gemini request after {retry + 1} retries")
                        raise e
                    else:
                        error_message = str(e)
                        print(f"Error: {e}")
                        print(f"Retrying Gemini request: {retry + 1}/{self.max_retries}")

                        # Check if this is a quota exhausted error with retryDelay
                        if e.code == 429 and "RESOURCE_EXHAUSTED" in error_message:
                            # Parse retryDelay from error message
                            retry_delay = parse_retry_delay(error_message)
                            print(f"🚫 [Gemini] API quota exhausted (429 RESOURCE_EXHAUSTED)")
                            print(f"📋 [Gemini] Error details: {error_message}")

                            # Wait for the specified delay
                            wait_with_progress(retry_delay, "API quota exhausted")
                        else:
                            # For other 503/429 errors, use default jitter
                            print(f"⏳ [Gemini] Waiting before retry...")
                            time.sleep(15 * random.uniform(0.8, 1.2))
                else:
                    raise e

        internal_messages = []
        if response.text:
            internal_messages.append(TextResult(text=response.text))

        if response.function_calls:
            for fn_call in response.function_calls:
                response_message_content = ToolCall(
                    tool_call_id=fn_call.id if fn_call.id else generate_tool_call_id(),
                    tool_name=fn_call.name,
                    tool_input=fn_call.args,
                )
                internal_messages.append(response_message_content)

        message_metadata = {
            "raw_response": response,
            "input_tokens": response.usage_metadata.prompt_token_count,
            "output_tokens": response.usage_metadata.candidates_token_count,
        }
        
        return internal_messages, message_metadata