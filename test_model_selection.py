#!/usr/bin/env python3
"""
Test script to verify model selection logic works correctly.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ii_agent.utils.constants import DEFAULT_MODEL
from ii_agent.llm import get_client

def test_default_model():
    """Test that the default model is now Gemini."""
    print(f"🔍 Testing default model...")
    print(f"DEFAULT_MODEL = {DEFAULT_MODEL}")
    
    # Check if it's a Gemini model
    if "gemini" in DEFAULT_MODEL.lower():
        print("✅ Default model is correctly set to Gemini")
        return True
    else:
        print("❌ Default model is not Gemini")
        return False

def test_model_mapping():
    """Test the model mapping logic."""
    print(f"\n🔍 Testing model mapping logic...")
    
    # Import the mapping function
    sys.path.insert(0, os.path.dirname(__file__))
    from ws_server import map_model_name_to_client
    
    test_cases = [
        ("gemini-2.5-flash", "gemini-direct"),
        ("claude-sonnet-4@20250514", "anthropic-direct"),
        ("ollama:llama3.2", "ollama-direct"),
    ]
    
    results = []
    for model_name, expected_client_type in test_cases:
        try:
            # Mock ws_content
            ws_content = {}
            client = map_model_name_to_client(model_name, ws_content)
            
            # Check client type
            client_type = type(client).__name__
            if "Gemini" in client_type and expected_client_type == "gemini-direct":
                print(f"✅ {model_name} -> {client_type} (correct)")
                results.append(True)
            elif "Anthropic" in client_type and expected_client_type == "anthropic-direct":
                print(f"✅ {model_name} -> {client_type} (correct)")
                results.append(True)
            elif "Ollama" in client_type and expected_client_type == "ollama-direct":
                print(f"✅ {model_name} -> {client_type} (correct)")
                results.append(True)
            else:
                print(f"❌ {model_name} -> {client_type} (expected {expected_client_type})")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {model_name} -> Error: {e}")
            results.append(False)
    
    return all(results)

def test_gemini_client():
    """Test that Gemini client can be created successfully."""
    print(f"\n🔍 Testing Gemini client creation...")
    
    try:
        client = get_client("gemini-direct", model_name="gemini-2.5-flash")
        print(f"✅ Gemini client created successfully: {type(client).__name__}")
        return True
    except Exception as e:
        print(f"❌ Failed to create Gemini client: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Model Selection Tests\n")
    
    results = []
    results.append(test_default_model())
    results.append(test_model_mapping())
    results.append(test_gemini_client())
    
    print(f"\n{'='*50}")
    print(f"📋 Test Summary:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print(f"\n🎉 All tests passed! Model selection should work correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Check the errors above.")
    
    return all(results)

if __name__ == "__main__":
    main()
