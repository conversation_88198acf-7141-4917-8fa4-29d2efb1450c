<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Switching Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1e1f23;
            color: white;
        }
        .container {
            background-color: #35363a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
        .error {
            background-color: #f44336;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #4CAF50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .log {
            background-color: #2a2b2f;
            padding: 10px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        select, input {
            background-color: #2a2b2f;
            color: white;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 5px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🔄 Model Switching Test</h1>
    
    <div class="container">
        <h2>Connection Status</h2>
        <div id="connectionStatus">Disconnected</div>
        <button id="connectBtn" onclick="connect()">Connect</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
    </div>

    <div class="container">
        <h2>Model Selection</h2>
        <select id="modelSelect">
            <option value="gemini-2.5-flash">Gemini 2.5 Flash</option>
            <option value="gemini-2.5-flash-preview-05-20">Gemini 2.5 Flash Preview</option>
            <option value="claude-sonnet-4@20250514">Claude Sonnet 4</option>
            <option value="ollama:llama3.2">Ollama Llama 3.2</option>
            <option value="ollama:gemma3:latest">Ollama Gemma 3</option>
            <option value="ollama:mistral">Ollama Mistral</option>
        </select>
        <button onclick="initializeAgent()">Initialize Agent</button>
        <button onclick="switchModel()">Switch Model</button>
    </div>

    <div class="container">
        <h2>Test Scenarios</h2>
        <button onclick="sendSimpleQuery()">Send Simple Query</button>
        <button onclick="sendComplexQuery()">Send Complex Query</button>
        <button onclick="simulateError()">Simulate API Error</button>
        <button onclick="continueTask()">Continue Task (Resume)</button>
    </div>

    <div class="container">
        <h2>Custom Query</h2>
        <input type="text" id="customQuery" placeholder="Enter your query..." style="width: 70%;">
        <button onclick="sendCustomQuery()">Send</button>
        <label>
            <input type="checkbox" id="resumeCheckbox"> Resume previous task
        </label>
    </div>

    <div class="container">
        <h2>Message Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="messageLog" class="log"></div>
    </div>

    <script>
        let socket = null;
        let deviceId = generateUUID();
        
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function log(message, type = 'info') {
            const logElement = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'send' ? '📤' : '📥';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function connect() {
            const wsUrl = `ws://localhost:8000/ws?device_id=${deviceId}`;
            socket = new WebSocket(wsUrl);
            
            socket.onopen = function(event) {
                document.getElementById('connectionStatus').textContent = 'Connected';
                document.getElementById('connectionStatus').className = 'success';
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                log('Connected to WebSocket server', 'success');
            };
            
            socket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                log(`Received: ${data.type} - ${JSON.stringify(data.content)}`, 'info');
                
                // Handle specific message types
                if (data.type === 'error') {
                    const errorContent = data.content;
                    if (errorContent.error_type === 'quota_exceeded') {
                        log('API quota exceeded! You can now switch models.', 'error');
                        showSwitchModelPrompt();
                    }
                }
            };
            
            socket.onclose = function(event) {
                document.getElementById('connectionStatus').textContent = 'Disconnected';
                document.getElementById('connectionStatus').className = 'error';
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                log('Disconnected from WebSocket server', 'error');
            };
            
            socket.onerror = function(error) {
                log(`WebSocket error: ${error}`, 'error');
            };
        }

        function disconnect() {
            if (socket) {
                socket.close();
            }
        }

        function sendMessage(type, content) {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('Not connected to WebSocket', 'error');
                return;
            }
            
            const message = { type, content };
            socket.send(JSON.stringify(message));
            log(`Sent: ${type} - ${JSON.stringify(content)}`, 'send');
        }

        function initializeAgent() {
            const modelName = document.getElementById('modelSelect').value;
            sendMessage('init_agent', {
                model_name: modelName,
                tool_args: {
                    deep_research: false,
                    pdf: true,
                    media_generation: true,
                    audio_generation: true,
                    browser: true,
                }
            });
        }

        function switchModel() {
            const modelName = document.getElementById('modelSelect').value;
            log(`Switching to model: ${modelName}`, 'info');
            initializeAgent();
        }

        function sendSimpleQuery() {
            sendMessage('query', {
                text: 'Hello! Please respond with a simple greeting.',
                resume: false,
                files: []
            });
        }

        function sendComplexQuery() {
            sendMessage('query', {
                text: 'Please create a Python script that reads a CSV file and generates a bar chart. Save it to the workspace.',
                resume: false,
                files: []
            });
        }

        function simulateError() {
            // This would normally be triggered by actual API quota exhaustion
            log('Simulating API quota error...', 'info');
            showSwitchModelPrompt();
        }

        function continueTask() {
            const lastQuery = 'Please create a Python script that reads a CSV file and generates a bar chart. Save it to the workspace.';
            sendMessage('query', {
                text: lastQuery,
                resume: true, // Continue from where we left off
                files: []
            });
        }

        function sendCustomQuery() {
            const query = document.getElementById('customQuery').value;
            const resume = document.getElementById('resumeCheckbox').checked;
            
            if (!query.trim()) {
                log('Please enter a query', 'error');
                return;
            }
            
            sendMessage('query', {
                text: query,
                resume: resume,
                files: []
            });
            
            document.getElementById('customQuery').value = '';
        }

        function showSwitchModelPrompt() {
            const currentModel = document.getElementById('modelSelect').value;
            const message = `Current model (${currentModel}) has quota issues. Switch to a different model?`;
            
            if (confirm(message)) {
                // Show model selection and switch
                const modelSelect = document.getElementById('modelSelect');
                modelSelect.style.backgroundColor = '#f44336';
                setTimeout(() => {
                    modelSelect.style.backgroundColor = '#2a2b2f';
                }, 2000);
                
                log('Please select a different model and click "Switch Model"', 'info');
            }
        }

        function clearLog() {
            document.getElementById('messageLog').textContent = '';
        }

        // Auto-connect on page load
        window.onload = function() {
            log('Page loaded. Click Connect to start testing.', 'info');
        };
    </script>
</body>
</html>
