#!/usr/bin/env python3
"""
Test script to verify Gemini and Ollama model implementations.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the src directory to the path
sys.path.insert(0, 'src')

from ii_agent.llm import get_client
from ii_agent.llm.base import TextPrompt


def test_gemini_free_api():
    """Test Gemini free API functionality."""
    print("🧪 Testing Gemini Free API...")
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY not set. Skipping Gemini test.")
        return False
    
    try:
        # Create Gemini client
        client = get_client("gemini-direct", model_name="gemini-2.5-flash-preview-05-20")
        
        # Test simple generation
        messages = [[TextPrompt(text="Hello! Please respond with 'Gemini is working!'")]]
        
        response, metadata = client.generate(
            messages=messages,
            max_tokens=50,
            system_prompt="You are a helpful assistant.",
            temperature=0.0
        )
        
        print(f"✅ Gemini response: {response[0].text}")
        print(f"📊 Metadata: {metadata}")
        return True
        
    except Exception as e:
        print(f"❌ Gemini test failed: {e}")
        return False


def test_ollama_local():
    """Test Ollama local model functionality."""
    print("\n🧪 Testing Ollama Local Models...")
    
    ollama_host = os.getenv("OLLAMA_HOST", "http://localhost:11434")
    print(f"🔗 Using Ollama host: {ollama_host}")
    
    try:
        # Create Ollama client
        client = get_client("ollama-direct", model_name="gemma3:latest", host=ollama_host)
        
        # Test simple generation
        messages = [[TextPrompt(text="Hello! Please respond with 'Ollama is working!'")]]
        
        response, metadata = client.generate(
            messages=messages,
            max_tokens=50,
            system_prompt="You are a helpful assistant.",
            temperature=0.0
        )
        
        print(f"✅ Ollama response: {response[0].text}")
        print(f"📊 Metadata: {metadata}")
        return True
        
    except Exception as e:
        print(f"❌ Ollama test failed: {e}")
        print("💡 Make sure Ollama is running and the model is pulled:")
        print("   ollama serve")
        print("   ollama pull llama3.2")
        return False


def test_model_factory():
    """Test the model factory function."""
    print("\n🧪 Testing Model Factory...")
    
    try:
        # Test all supported client types
        clients = [
            ("anthropic-direct", "claude-sonnet-4@20250514"),
            ("gemini-direct", "gemini-2.5-flash"),
            ("ollama-direct", "llama3.2"),
        ]
        
        for client_name, model_name in clients:
            try:
                client = get_client(client_name, model_name=model_name)
                print(f"✅ {client_name} client created successfully")
            except Exception as e:
                print(f"⚠️  {client_name} client creation failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model factory test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting II-Agent Model Tests\n")
    
    results = []
    
    # Test model factory
    results.append(test_model_factory())
    
    # Test Gemini free API
    results.append(test_gemini_free_api())
    
    # Test Ollama local models
    results.append(test_ollama_local())
    
    # Summary
    print("\n" + "="*50)
    print("📋 Test Summary:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print("\n🎉 All tests passed! Your II-Agent setup is ready.")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        print("\n💡 Setup Tips:")
        print("   - For Gemini: Get a free API key from https://aistudio.google.com/app/apikey")
        print("   - For Ollama: Install from https://ollama.ai and run 'ollama serve'")


if __name__ == "__main__":
    main()
